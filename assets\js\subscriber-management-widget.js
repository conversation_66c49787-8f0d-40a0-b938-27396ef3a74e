(function($) {
    'use strict';

    // ===========================================================================================
    // PAYMENT GATEWAY CONNECTION CHECKLIST & ADVANCED MONITORING SYSTEM
    // Accessible via console: window.PaymentGatewayChecker
    // ===========================================================================================

    window.PaymentGatewayChecker = {
        version: '1.0.0',
        lastCheck: null,
        isInitialized: false,

        // Main checklist function
        runChecklist: function() {
            console.log('%c🏦 PAYMENT GATEWAY CONNECTION CHECKLIST', 'color: #2196F3; font-size: 16px; font-weight: bold; background: #E3F2FD; padding: 8px;');
            console.log('%cStarting comprehensive payment gateway analysis...', 'color: #666; font-style: italic;');

            this.lastCheck = new Date();
            const results = {};

            // 1. Gateway Configuration Check
            results.configuration = this.checkGatewayConfiguration();

            // 2. JavaScript Dependencies
            results.dependencies = this.checkJavaScriptDependencies();

            // 3. API Connectivity
            results.connectivity = this.checkAPIConnectivity();

            // 4. Security & SSL
            results.security = this.checkSecurityConfiguration();

            // 5. Error Handling
            results.errorHandling = this.checkErrorHandling();

            // 6. Performance Metrics
            results.performance = this.checkPerformanceMetrics();

            // 7. WordPress Integration
            results.wordpressIntegration = this.checkWordPressIntegration();

            // Generate comprehensive report
            this.generateReport(results);

            return results;
        },

        // 1. Gateway Configuration Check
        checkGatewayConfiguration: function() {
            console.log('\n%c1️⃣ GATEWAY CONFIGURATION CHECK', 'color: #FF9800; font-weight: bold;');

            const config = {
                paypal: this.checkPayPalConfig(),
                stripe: this.checkStripeConfig(),
                wordpress: this.checkWordPressConfig(),
                ajax: this.checkAjaxConfig()
            };

            const allValid = Object.values(config).every(c => c.status === 'success');
            console.log(`%c${allValid ? '✅' : '❌'} Configuration Status: ${allValid ? 'ALL VALID' : 'ISSUES FOUND'}`,
                       `color: ${allValid ? 'green' : 'red'}; font-weight: bold;`);

            return { status: allValid ? 'success' : 'error', details: config };
        },

        checkPayPalConfig: function() {
            const checks = [
                { name: 'PayPal SDK', test: () => window.paypal !== undefined },
                { name: 'PayPal Button Container', test: () => $('#paypal-button-container').length > 0 },
                { name: 'PayPal Client ID', test: () => this.getPayPalClientId() !== null },
                { name: 'PayPal Environment', test: () => this.getPayPalEnvironment() !== null }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  PayPal: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        checkStripeConfig: function() {
            const checks = [
                { name: 'Stripe SDK', test: () => window.Stripe !== undefined },
                { name: 'Stripe Publishable Key', test: () => this.getStripePublishableKey() !== null },
                { name: 'Stripe Elements Container', test: () => $('#stripe-card-element').length > 0 },
                { name: 'Stripe Instance', test: () => window.stripe !== undefined }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  Stripe: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        checkWordPressConfig: function() {
            const checks = [
                { name: 'WordPress AJAX URL', test: () => typeof subscriberManagementAjax !== 'undefined' && subscriberManagementAjax.ajax_url },
                { name: 'WordPress Nonce', test: () => typeof subscriberManagementAjax !== 'undefined' && subscriberManagementAjax.nonce },
                { name: 'jQuery Loaded', test: () => typeof jQuery !== 'undefined' },
                { name: 'Widget Container', test: () => $('.subscriber-management-widget-container').length > 0 }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  WordPress: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        checkAjaxConfig: function() {
            const checks = [
                { name: 'AJAX URL Valid', test: () => this.isValidUrl(subscriberManagementAjax?.ajax_url) },
                { name: 'Nonce Present', test: () => subscriberManagementAjax?.nonce?.length > 0 },
                { name: 'jQuery AJAX Available', test: () => typeof $.ajax === 'function' },
                { name: 'CORS Headers', test: () => this.checkCORSHeaders() }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  AJAX: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        // 2. JavaScript Dependencies Check
        checkJavaScriptDependencies: function() {
            console.log('\n%c2️⃣ JAVASCRIPT DEPENDENCIES CHECK', 'color: #FF9800; font-weight: bold;');

            const dependencies = [
                { name: 'jQuery', global: 'jQuery', version: () => jQuery.fn.jquery },
                { name: 'PayPal SDK', global: 'paypal', version: () => 'PayPal JS SDK' },
                { name: 'Stripe SDK', global: 'Stripe', version: () => 'Stripe.js v3' },
                { name: 'WordPress AJAX', global: 'subscriberManagementAjax', version: () => 'WordPress AJAX' }
            ];

            const results = dependencies.map(dep => {
                const exists = window[dep.global] !== undefined;
                return {
                    name: dep.name,
                    exists: exists,
                    version: exists && dep.version ? dep.version() : 'N/A'
                };
            });

            const allLoaded = results.every(r => r.exists);
            console.log(`%c${allLoaded ? '✅' : '❌'} Dependencies Status: ${allLoaded ? 'ALL LOADED' : 'MISSING DEPENDENCIES'}`,
                       `color: ${allLoaded ? 'green' : 'red'}; font-weight: bold;`);

            results.forEach(r => console.log(`  ${r.exists ? '✓' : '✗'} ${r.name} (${r.version})`));

            return { status: allLoaded ? 'success' : 'error', dependencies: results };
        },

        // 3. API Connectivity Check
        checkAPIConnectivity: function() {
            console.log('\n%c3️⃣ API CONNECTIVITY CHECK', 'color: #FF9800; font-weight: bold;');

            const connectivity = {
                wordpress: this.testWordPressAPI(),
                paypal: this.testPayPalAPI(),
                stripe: this.testStripeAPI(),
                ssl: this.checkSSLStatus()
            };

            return { status: 'info', details: connectivity };
        },

        testWordPressAPI: function() {
            console.log('  Testing WordPress API connectivity...');
            // This would be an async test in real implementation
            const hasValidEndpoint = subscriberManagementAjax?.ajax_url?.includes('admin-ajax.php');
            console.log(`    ${hasValidEndpoint ? '✓' : '✗'} WordPress AJAX Endpoint`);
            return { status: hasValidEndpoint ? 'success' : 'error' };
        },

        testPayPalAPI: function() {
            console.log('  Testing PayPal API connectivity...');
            const hasPayPal = window.paypal !== undefined;
            console.log(`    ${hasPayPal ? '✓' : '✗'} PayPal SDK Loaded`);
            return { status: hasPayPal ? 'success' : 'error' };
        },

        testStripeAPI: function() {
            console.log('  Testing Stripe API connectivity...');
            const hasStripe = window.Stripe !== undefined;
            console.log(`    ${hasStripe ? '✓' : '✗'} Stripe SDK Loaded`);
            return { status: hasStripe ? 'success' : 'error' };
        },

        // 4. Security Configuration Check
        checkSecurityConfiguration: function() {
            console.log('\n%c4️⃣ SECURITY CONFIGURATION CHECK', 'color: #FF9800; font-weight: bold;');

            const security = {
                ssl: this.checkSSLStatus(),
                nonce: this.checkNonceSecurity(),
                cors: this.checkCORSHeaders(),
                csp: this.checkCSPHeaders()
            };

            const allSecure = Object.values(security).every(s => s.status === 'success');
            console.log(`%c${allSecure ? '✅' : '⚠️'} Security Status: ${allSecure ? 'SECURE' : 'REVIEW NEEDED'}`,
                       `color: ${allSecure ? 'green' : 'orange'}; font-weight: bold;`);

            return { status: allSecure ? 'success' : 'warning', details: security };
        },

        checkSSLStatus: function() {
            const isSSL = location.protocol === 'https:';
            console.log(`  ${isSSL ? '✓' : '✗'} SSL/HTTPS Enabled`);
            return { status: isSSL ? 'success' : 'error', ssl: isSSL };
        },

        checkNonceSecurity: function() {
            const hasNonce = subscriberManagementAjax?.nonce?.length > 0;
            console.log(`  ${hasNonce ? '✓' : '✗'} WordPress Nonce Present`);
            return { status: hasNonce ? 'success' : 'error', nonce: hasNonce };
        },

        checkCORSHeaders: function() {
            // This is a simplified check
            console.log('  ✓ CORS Headers (requires server response)');
            return { status: 'success' };
        },

        checkCSPHeaders: function() {
            const hasCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null;
            console.log(`  ${hasCSP ? '✓' : '○'} Content Security Policy ${hasCSP ? 'Configured' : 'Not Detected'}`);
            return { status: hasCSP ? 'success' : 'info', csp: hasCSP };
        },

        // 5. Error Handling Check
        checkErrorHandling: function() {
            console.log('\n%c5️⃣ ERROR HANDLING CHECK', 'color: #FF9800; font-weight: bold;');

            const errorHandling = {
                globalHandler: this.checkGlobalErrorHandler(),
                ajaxHandler: this.checkAjaxErrorHandler(),
                paymentHandler: this.checkPaymentErrorHandler(),
                userFeedback: this.checkUserFeedbackSystem()
            };

            const allConfigured = Object.values(errorHandling).every(e => e.status === 'success');
            console.log(`%c${allConfigured ? '✅' : '⚠️'} Error Handling: ${allConfigured ? 'COMPREHENSIVE' : 'PARTIAL'}`,
                       `color: ${allConfigured ? 'green' : 'orange'}; font-weight: bold;`);

            return { status: allConfigured ? 'success' : 'warning', details: errorHandling };
        },

        checkGlobalErrorHandler: function() {
            const hasHandler = window.onerror !== null || window.addEventListener;
            console.log(`  ${hasHandler ? '✓' : '✗'} Global Error Handler`);
            return { status: hasHandler ? 'success' : 'error' };
        },

        checkAjaxErrorHandler: function() {
            // Check if AJAX calls have error handlers
            console.log('  ✓ AJAX Error Handling (implemented in functions)');
            return { status: 'success' };
        },

        checkPaymentErrorHandler: function() {
            console.log('  ✓ Payment Error Handling (implemented in gateway functions)');
            return { status: 'success' };
        },

        checkUserFeedbackSystem: function() {
            const hasFeedback = $('#subscriber-feedback-message').length > 0;
            console.log(`  ${hasFeedback ? '✓' : '✗'} User Feedback System`);
            return { status: hasFeedback ? 'success' : 'error' };
        },

        // 6. Performance Metrics Check
        checkPerformanceMetrics: function() {
            console.log('\n%c6️⃣ PERFORMANCE METRICS CHECK', 'color: #FF9800; font-weight: bold;');

            const performance = {
                loadTimes: this.checkLoadTimes(),
                memory: this.checkMemoryUsage(),
                domSize: this.checkDOMSize(),
                optimization: this.checkOptimizations()
            };

            console.log('  Performance metrics collected');
            return { status: 'info', details: performance };
        },

        checkLoadTimes: function() {
            const timing = window.performance?.timing;
            if (timing) {
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                console.log(`  ⏱️ Page Load Time: ${loadTime}ms`);
                return { loadTime: loadTime, status: loadTime < 3000 ? 'good' : 'slow' };
            }
            return { status: 'unknown' };
        },

        checkMemoryUsage: function() {
            if ('memory' in performance) {
                const memory = performance.memory;
                console.log(`  💾 Memory Usage: ${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`);
                return { used: memory.usedJSHeapSize, limit: memory.jsHeapSizeLimit };
            }
            return { status: 'unavailable' };
        },

        checkDOMSize: function() {
            const domSize = document.getElementsByTagName('*').length;
            console.log(`  📄 DOM Elements: ${domSize}`);
            return { size: domSize, status: domSize < 1500 ? 'optimal' : 'large' };
        },

        checkOptimizations: function() {
            const optimizations = [
                { name: 'Throttling', exists: typeof throttle === 'function' },
                { name: 'Debouncing', exists: typeof debounce === 'function' },
                { name: 'RequestAnimationFrame', exists: 'requestAnimationFrame' in window }
            ];

            optimizations.forEach(opt => {
                console.log(`    ${opt.exists ? '✓' : '✗'} ${opt.name}`);
            });

            return { optimizations: optimizations };
        },

        // 7. WordPress Integration Check
        checkWordPressIntegration: function() {
            console.log('\n%c7️⃣ WORDPRESS INTEGRATION CHECK', 'color: #FF9800; font-weight: bold;');

            const integration = {
                hooks: this.checkWordPressHooks(),
                actions: this.checkWordPressActions(),
                filters: this.checkWordPressFilters(),
                database: this.checkDatabaseTables()
            };

            const allIntegrated = Object.values(integration).every(i => i.status === 'success');
            console.log(`%c${allIntegrated ? '✅' : '⚠️'} WordPress Integration: ${allIntegrated ? 'COMPLETE' : 'PARTIAL'}`,
                       `color: ${allIntegrated ? 'green' : 'orange'}; font-weight: bold;`);

            return { status: allIntegrated ? 'success' : 'warning', details: integration };
        },

        checkWordPressHooks: function() {
            console.log('  ✓ WordPress Hooks (server-side)');
            return { status: 'success' };
        },

        checkWordPressActions: function() {
            const hasActions = subscriberManagementAjax?.nonce !== undefined;
            console.log(`  ${hasActions ? '✓' : '✗'} WordPress AJAX Actions`);
            return { status: hasActions ? 'success' : 'error' };
        },

        checkWordPressFilters: function() {
            console.log('  ✓ WordPress Filters (server-side)');
            return { status: 'success' };
        },

        checkDatabaseTables: function() {
            console.log('  ✓ Database Tables (requires server verification)');
            return { status: 'success' };
        },

        // Report Generation
        generateReport: function(results) {
            console.log('\n%c📊 COMPREHENSIVE REPORT', 'color: #4CAF50; font-size: 14px; font-weight: bold; background: #E8F5E8; padding: 8px;');

            const summary = this.generateSummary(results);
            const recommendations = this.generateRecommendations(results);

            console.log('\n%c📋 SUMMARY:', 'color: #2196F3; font-weight: bold;');
            Object.entries(summary).forEach(([key, value]) => {
                const icon = value.status === 'success' ? '✅' : value.status === 'warning' ? '⚠️' : '❌';
                console.log(`  ${icon} ${key}: ${value.message}`);
            });

            if (recommendations.length > 0) {
                console.log('\n%c💡 RECOMMENDATIONS:', 'color: #FF9800; font-weight: bold;');
                recommendations.forEach((rec, index) => {
                    console.log(`  ${index + 1}. ${rec}`);
                });
            }

            console.log('\n%c🔧 QUICK ACTIONS:', 'color: #9C27B0; font-weight: bold;');
            console.log('  • Run PaymentGatewayChecker.testPayment() - Test payment flow');
            console.log('  • Run PaymentGatewayChecker.simulateError() - Test error handling');
            console.log('  • Run PaymentGatewayChecker.exportReport() - Export full report');
            console.log('  • Run PaymentGatewayChecker.fixCommonIssues() - Auto-fix common problems');

            console.log(`\n%c✨ Report generated at ${this.lastCheck.toLocaleString()}`, 'color: #666; font-style: italic;');
        },

        generateSummary: function(results) {
            return {
                'Configuration': {
                    status: results.configuration.status,
                    message: results.configuration.status === 'success' ? 'All gateways properly configured' : 'Configuration issues detected'
                },
                'Dependencies': {
                    status: results.dependencies.status,
                    message: results.dependencies.status === 'success' ? 'All dependencies loaded' : 'Missing dependencies'
                },
                'Security': {
                    status: results.security.status,
                    message: results.security.status === 'success' ? 'Security measures in place' : 'Review security settings'
                },
                'Error Handling': {
                    status: results.errorHandling.status,
                    message: results.errorHandling.status === 'success' ? 'Comprehensive error handling' : 'Improve error handling'
                },
                'WordPress Integration': {
                    status: results.wordpressIntegration.status,
                    message: results.wordpressIntegration.status === 'success' ? 'Fully integrated with WordPress' : 'Integration issues'
                }
            };
        },

        generateRecommendations: function(results) {
            const recommendations = [];

            if (results.configuration.status !== 'success') {
                recommendations.push('Review and fix payment gateway configuration');
            }

            if (results.dependencies.status !== 'success') {
                recommendations.push('Ensure all required JavaScript libraries are loaded');
            }

            if (results.security.status !== 'success') {
                recommendations.push('Implement SSL and security best practices');
            }

            if (location.protocol !== 'https:') {
                recommendations.push('Enable HTTPS for secure payment processing');
            }

            return recommendations;
        },

        // Additional Utility Functions
        testPayment: function(amount = 10, method = 'paypal') {
            console.log(`%c🧪 Testing payment flow: €${amount} via ${method}`, 'color: #2196F3; font-weight: bold;');

            // This would simulate a payment without actually processing it
            const testData = {
                action: 'test_payment',
                amount: amount,
                method: method,
                test_mode: true
            };

            console.log('Test payment data:', testData);
            console.log('✓ Payment simulation completed (no actual charge)');

            return testData;
        },

        simulateError: function(errorType = 'network') {
            console.log(`%c🐛 Simulating ${errorType} error...`, 'color: #F44336; font-weight: bold;');

            const errors = {
                network: () => {
                    console.error('Simulated network error: Connection timeout');
                    if (window.showFeedbackMessage) {
                        showFeedbackMessage('Network error simulated', 'error');
                    }
                },
                payment: () => {
                    console.error('Simulated payment error: Invalid card');
                    if (window.showFeedbackMessage) {
                        showFeedbackMessage('Payment error simulated', 'error');
                    }
                },
                validation: () => {
                    console.error('Simulated validation error: Invalid amount');
                    if (window.showFeedbackMessage) {
                        showFeedbackMessage('Validation error simulated', 'error');
                    }
                }
            };

            if (errors[errorType]) {
                errors[errorType]();
            } else {
                console.error(`Unknown error type: ${errorType}`);
            }
        },

        exportReport: function() {
            const report = this.runChecklist();
            const exportData = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                results: report
            };

            console.log('%c📁 Exporting report...', 'color: #4CAF50; font-weight: bold;');
            console.log('Export data:', exportData);

            // Create downloadable file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `payment-gateway-report-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('✅ Report exported successfully');
        },

        fixCommonIssues: function() {
            console.log('%c🔧 Auto-fixing common issues...', 'color: #FF9800; font-weight: bold;');

            let fixesApplied = 0;

            // Fix missing feedback element
            if ($('#subscriber-feedback-message').length === 0) {
                $('body').append('<div id="subscriber-feedback-message" style="display:none;"></div>');
                console.log('  ✓ Added missing feedback message element');
                fixesApplied++;
            }

            // Fix missing modal overlay styles
            if ($('#fast-confirm-modal').length === 0) {
                const modalCSS = `
                    <style>
                        .fast-modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center; }
                        .fast-modal-content { background: white; padding: 20px; border-radius: 8px; max-width: 400px; text-align: center; }
                        .fast-modal-buttons { margin-top: 20px; }
                        .fast-btn { padding: 10px 20px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; }
                        .fast-btn-confirm { background: #4CAF50; color: white; }
                        .fast-btn-cancel { background: #f44336; color: white; }
                    </style>
                `;
                $('head').append(modalCSS);
                console.log('  ✓ Added modal overlay styles');
                fixesApplied++;
            }

            // Check SSL redirect
            if (location.protocol === 'http:' && location.hostname !== 'localhost') {
                console.log('  ⚠️ SSL not enabled (requires server configuration)');
            }

            console.log(`%c✅ Applied ${fixesApplied} fixes`, 'color: #4CAF50; font-weight: bold;');

            if (fixesApplied === 0) {
                console.log('  No common issues found to fix');
            }
        },

        // Helper Functions
        getPayPalClientId: function() {
            // This would typically come from WordPress options or meta tags
            const metaTag = document.querySelector('meta[name="paypal-client-id"]');
            return metaTag ? metaTag.content : null;
        },

        getPayPalEnvironment: function() {
            // Check if PayPal is loaded in sandbox or production mode
            return window.paypal ? 'loaded' : null;
        },

        getStripePublishableKey: function() {
            // This would typically come from WordPress options or meta tags
            const metaTag = document.querySelector('meta[name="stripe-publishable-key"]');
            return metaTag ? metaTag.content : null;
        },

        isValidUrl: function(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }
    };

    // Initialize checker when DOM is ready
    $(document).ready(function() {
        window.PaymentGatewayChecker.isInitialized = true;

        // Add console welcome message
        console.log('%c🏦 Payment Gateway Checker Initialized', 'color: #4CAF50; font-weight: bold; font-size: 14px;');
        console.log('%cRun PaymentGatewayChecker.runChecklist() to start comprehensive analysis', 'color: #2196F3;');
        console.log('%cAvailable commands:', 'color: #666;');
        console.log('  • PaymentGatewayChecker.runChecklist() - Full gateway analysis');
        console.log('  • PaymentGatewayChecker.testPayment(amount, method) - Test payment flow');
        console.log('  • PaymentGatewayChecker.simulateError(type) - Test error handling');
        console.log('  • PaymentGatewayChecker.exportReport() - Export comprehensive report');
        console.log('  • PaymentGatewayChecker.fixCommonIssues() - Auto-fix common problems');
    });

    // ===========================================================================================
    // END PAYMENT GATEWAY CHECKER
    // ===========================================================================================

    $(document).ready(function() {
        // Inizializzazione del widget
        initSubscriberManagementWidget();
    });

    function initSubscriberManagementWidget() {
        // Menu navigation with throttling
        $('.menu-item').on('click', throttle(function() {
            const section = $(this).data('section');
            switchSection(section);
        }, 200));

        // Form submission per aggiornamento dati
        $('#access-data-form').on('submit', function(e) {
            e.preventDefault();
            updateSubscriberData();
        });

        // Amount buttons per ricarica crediti with throttling
        $('.amount-btn').on('click', throttle(function() {
            const amount = $(this).data('amount');
            selectAmount(amount);
        }, 100));

        // Custom amount input with debouncing
        $('#custom-amount-input').on('input', debounce(function() {
            const amount = parseFloat($(this).val());
            if (amount >= 5) {
                selectAmount(amount);
                $('.amount-btn').removeClass('selected');
            }
        }, 300));

        // Payment method selection with throttling
        $('.payment-method').on('click', throttle(function() {
            const method = $(this).data('method');
            selectPaymentMethod(method);
        }, 100));

        // Proceed with recharge - ultra-optimized with immediate async handling
        $('#proceed-recharge-btn').on('click', function(e) {
            e.preventDefault();

            // Immediate UI feedback and validation
            const $btn = $(this);
            if ($btn.prop('disabled') || $btn.hasClass('processing')) {
                return false; // Prevent double-clicks
            }

            // Use requestAnimationFrame for better performance
            requestAnimationFrame(function() {
                proceedWithRecharge();
            });

            return false;
        });
    }

    function switchSection(section) {
        // Update menu
        $('.menu-item').removeClass('active');
        $('.menu-item[data-section="' + section + '"]').addClass('active');

        // Update content
        $('.content-section').removeClass('active');
        $('#' + section + '-section').addClass('active');
    }

    function updateSubscriberData() {
        const formData = {
            action: 'update_subscriber_data',
            nonce: subscriberManagementAjax.nonce,
            subscriber_id: $('input[name="subscriber_id"]').val(),
            name: $('#subscriber-name').val(),
            surname: $('#subscriber-surname').val(),
            phone: $('#subscriber-phone').val()
        };

        // Show loading state
        const $submitBtn = $('#access-data-form button[type="submit"]');
        const originalText = $submitBtn.html();
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Aggiornamento...').prop('disabled', true);

        $.ajax({
            url: subscriberManagementAjax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(subscriberManagementAjax.messages.update_error, 'error');
            },
            complete: function() {
                // Restore button state
                $submitBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    function selectAmount(amount) {
        $('#selected-amount').val(amount);
        $('.amount-btn').removeClass('selected');
        $('.amount-btn[data-amount="' + amount + '"]').addClass('selected');

        // Clear custom input if predefined amount is selected
        if ($('.amount-btn[data-amount="' + amount + '"]').length > 0) {
            $('#custom-amount-input').val('');
        }

        updateRechargeButton();
    }

    function selectPaymentMethod(method) {
        $('#selected-method').val(method);
        $('.payment-method').removeClass('selected');
        $('.payment-method[data-method="' + method + '"]').addClass('selected');
        updateRechargeButton();
    }

    function updateRechargeButton() {
        const amount = $('#selected-amount').val();
        const method = $('#selected-method').val();
        const $btn = $('#proceed-recharge-btn');

        if (amount && method && parseFloat(amount) >= 5) {
            $btn.prop('disabled', false);
            $btn.html('<i class="fas fa-shopping-cart"></i> Ricarica €' + parseFloat(amount).toFixed(2));
        } else {
            $btn.prop('disabled', true);
            $btn.html('<i class="fas fa-shopping-cart"></i> Procedi con la Ricarica');
        }
    }

    function proceedWithRecharge() {
        // Prevent multiple simultaneous executions with immediate check
        const $btn = $('#proceed-recharge-btn');
        if ($btn.hasClass('processing')) {
            return;
        }

        // Cache DOM queries for better performance
        const amount = parseFloat($('#selected-amount').val());
        const method = $('#selected-method').val();
        const subscriberId = $('#subscriber-id').val();

        // Batch validation checks
        if (!amount || !method || !subscriberId || amount < 5) {
            const errorMsg = !amount || !method || !subscriberId
                ? 'Seleziona un importo e un metodo di pagamento'
                : 'L\'importo minimo è €5.00';
            showFeedbackMessage(errorMsg, 'error');
            return;
        }

        // Mark as processing immediately
        $btn.addClass('processing');

        // Use fast custom modal instead of blocking confirm()
        const confirmMessage = 'Confermi la ricarica di €' + amount.toFixed(2) + ' tramite ' + getPaymentMethodName(method) + '?';

        requestAsyncConfirmation(
            confirmMessage,
            function() {
                // User confirmed - proceed with recharge
                executeRecharge(amount, method, subscriberId, $btn);
            },
            function() {
                // User cancelled - restore button state immediately
                $btn.removeClass('processing');
            }
        );
    }    function executeRecharge(amount, method, subscriberId, $btn) {
        const originalText = $btn.html();

        // Immediate UI update for better responsiveness
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Elaborazione...').prop('disabled', true);

        const rechargeData = {
            action: 'recharge_credits',
            nonce: subscriberManagementAjax.nonce,
            subscriber_id: subscriberId,
            amount: amount,
            method: method
        };

        // Performance optimized AJAX with shorter timeout
        $.ajax({
            url: subscriberManagementAjax.ajax_url,
            type: 'POST',
            data: rechargeData,
            timeout: 20000, // Reduced to 20 seconds for faster feedback
            cache: false,
            success: function(response) {
                if (response && response.success) {
                    const message = (response.data && response.data.message)
                        ? response.data.message
                        : 'Ricarica completata con successo';
                    showFeedbackMessage(message, 'success');

                    // Update credit display with animation
                    if (response.data && response.data.new_credit) {
                        updateCreditDisplay(response.data.new_credit);

                        // Trigger sync after credit update to ensure consistency
                        setTimeout(function() {
                            if (typeof syncCreditDisplays === 'function') {
                                syncCreditDisplays();
                            }
                        }, 500);
                    }

                    // Reset form
                    resetRechargeForm();
                } else {
                    // Improved error handling
                    const errorMessage = (response && response.data && response.data.message)
                        ? response.data.message
                        : (subscriberManagementAjax.messages && subscriberManagementAjax.messages.recharge_error)
                        ? subscriberManagementAjax.messages.recharge_error
                        : 'Errore durante la ricarica. Riprova.';
                    showFeedbackMessage(errorMessage, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Credit recharge AJAX error:', {xhr, status, error});

                let errorMessage = 'Errore durante la ricarica. Riprova.';
                if (status === 'timeout') {
                    errorMessage = 'Timeout della richiesta. Riprova più tardi.';
                } else if (xhr.status === 0) {
                    errorMessage = 'Problema di connessione. Verifica la tua connessione internet.';
                } else if (subscriberManagementAjax.messages && subscriberManagementAjax.messages.recharge_error) {
                    errorMessage = subscriberManagementAjax.messages.recharge_error;
                }

                showFeedbackMessage(errorMessage, 'error');
            },
            complete: function() {
                // Faster button state restoration
                requestAnimationFrame(function() {
                    $btn.html(originalText)
                        .prop('disabled', false)
                        .removeClass('processing');
                });
            }
        });
    }

    function resetRechargeForm() {
        $('#selected-amount').val('');
        $('#selected-method').val('');
        $('#custom-amount-input').val('');
        $('.amount-btn').removeClass('selected');
        $('.payment-method').removeClass('selected');
        updateRechargeButton();
    }

    function getPaymentMethodName(method) {
        const methods = {
            'paypal': 'PayPal',
            'stripe': 'Carta di Credito'
        };
        return methods[method] || method;
    }

    function showFeedbackMessage(message, type) {
        const $feedback = $('#subscriber-feedback-message');

        $feedback
            .removeClass('success error')
            .addClass(type)
            .text(message)
            .fadeIn();

        // Auto hide after 5 seconds
        setTimeout(function() {
            $feedback.fadeOut();
        }, 5000);
    }

    // Utility function to format currency
    function formatCurrency(amount) {
        return '€' + parseFloat(amount).toFixed(2).replace('.', ',');
    }

    // Performance optimization utilities
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // Custom fast confirmation modal - replaces blocking confirm()
    function showConfirmationModal(message, onConfirm, onCancel) {
        // Create modal HTML only if it doesn't exist
        let $modal = $('#fast-confirm-modal');
        if ($modal.length === 0) {
            $modal = $(`
                <div id="fast-confirm-modal" class="fast-modal-overlay" style="display: none;">
                    <div class="fast-modal-content">
                        <div class="fast-modal-message"></div>
                        <div class="fast-modal-buttons">
                            <button class="fast-btn fast-btn-confirm">Conferma</button>
                            <button class="fast-btn fast-btn-cancel">Annulla</button>
                        </div>
                    </div>
                </div>
            `);
            $('body').append($modal);
        }

        // Update message and show
        $modal.find('.fast-modal-message').text(message);
        $modal.fadeIn(200);

        // Handle buttons (remove previous handlers first)
        $modal.off('click.confirm');
        $modal.on('click.confirm', '.fast-btn-confirm', function() {
            $modal.fadeOut(100);
            onConfirm();
        });
        $modal.on('click.confirm', '.fast-btn-cancel, .fast-modal-overlay', function(e) {
            if (e.target === this) {
                $modal.fadeOut(100);
                if (onCancel) onCancel();
            }
        });

        // ESC key support
        $(document).off('keydown.fastmodal').on('keydown.fastmodal', function(e) {
            if (e.keyCode === 27) { // ESC
                $modal.fadeOut(100);
                $(document).off('keydown.fastmodal');
                if (onCancel) onCancel();
            }
        });
    }

    // Async confirmation dialog to prevent blocking
    function requestAsyncConfirmation(message, onConfirm, onCancel) {
        // Use custom modal instead of blocking confirm()
        showConfirmationModal(message, onConfirm, onCancel);
    }

    // Optimized animated credit display update
    function updateCreditDisplay(newCredit) {
        // Target specific credit display elements with unique IDs
        const consumptionCreditElement = document.getElementById('consumption-credit-display');
        const rechargeCreditElement = document.getElementById('recharge-credit-display');

        // Format the credit value consistently
        const formattedCredit = '€' + parseFloat(newCredit).toFixed(2).replace('.', ',');

        // Update consumption section credit display
        if (consumptionCreditElement) {
            updateSingleCreditElement(consumptionCreditElement, formattedCredit);
        }

        // Update recharge section credit display
        if (rechargeCreditElement) {
            updateSingleCreditElement(rechargeCreditElement, formattedCredit);
        }

        // Fallback: Update any other credit value elements
        const additionalCreditElements = document.querySelectorAll('.credit-value:not(#consumption-credit-display):not(#recharge-credit-display)');
        additionalCreditElements.forEach(element => {
            updateSingleCreditElement(element, formattedCredit);
        });

        console.log('Credit display updated:', formattedCredit);
    }

    // Helper function to update a single credit element with animation
    function updateSingleCreditElement(element, formattedCredit) {
        if (!element) return;

        // Determina se l'elemento appartiene al subscriber management widget
        const isSubscriberWidget = element.closest('.subscriber-management-widget-container') !== null;

        // Use requestAnimationFrame for smoother animations
        requestAnimationFrame(function() {
            // Add animation class with widget-specific classes for isolation
            element.classList.add('credit-updating');

            // Aggiungi anche la classe specifica per il subscriber widget se applicabile
            if (isSubscriberWidget) {
                element.classList.add('subscriber-credit-updating');
            }

            // Update the value with optimized timing
            requestAnimationFrame(function() {
                element.textContent = formattedCredit;

                // Remove animation classes after animation completes
                setTimeout(function() {
                    element.classList.remove('credit-updating');
                    if (isSubscriberWidget) {
                        element.classList.remove('subscriber-credit-updating');
                    }
                }, 300);
            });
        });
    }

    // Credit synchronization function
    function syncCreditDisplays() {
        try {
            const consumptionCredit = document.getElementById('consumption-credit-display');
            const rechargeCredit = document.getElementById('recharge-credit-display');

            if (!consumptionCredit || !rechargeCredit) {
                console.warn('Credit display elements not found during sync');
                return;
            }

            // Get the current values
            const consumptionValue = consumptionCredit.textContent.trim();
            const rechargeValue = rechargeCredit.textContent.trim();

            console.log('Credit sync check:', {
                consumption: consumptionValue,
                recharge: rechargeValue
            });

            // If values are different, prioritize the most recent one
            // or fetch from server to get the correct value
            if (consumptionValue !== rechargeValue) {
                console.warn('Credit values are inconsistent, syncing from server...');

                // Add visual indicator during sync
                consumptionCredit.classList.add('credit-updating');
                rechargeCredit.classList.add('credit-updating');

                fetchCurrentCreditValue()
                    .then(credit => {
                        updateCreditDisplay(credit);
                        console.log('Credit synchronization completed successfully');
                    })
                    .catch(error => {
                        console.error('Failed to fetch current credit:', error);
                        // Fallback: use consumption section value as it's typically more reliable
                        const numericValue = extractNumericValue(consumptionValue);
                        if (numericValue !== null) {
                            updateCreditDisplay(numericValue.toFixed(2));
                            console.log('Using fallback credit value from consumption section');
                        }
                    })
                    .finally(() => {
                        // Remove visual indicators
                        setTimeout(() => {
                            consumptionCredit.classList.remove('credit-updating');
                            rechargeCredit.classList.remove('credit-updating');
                        }, 300);
                    });
            } else {
                console.log('Credit values are synchronized:', consumptionValue);
            }
        } catch (error) {
            console.error('Error during credit synchronization:', error);
        }
    }

    // Extract numeric value from formatted credit string
    function extractNumericValue(creditString) {
        if (!creditString) return null;

        // Remove currency symbol and convert comma to dot
        const numericString = creditString.replace(/[€\s]/g, '').replace(',', '.');
        const value = parseFloat(numericString);

        return isNaN(value) ? null : value;
    }

    // Fetch current credit value from server with retry logic
    function fetchCurrentCreditValue(retryCount = 0) {
        return new Promise((resolve, reject) => {
            if (!subscriberManagementAjax) {
                reject('AJAX configuration not available');
                return;
            }

            $.ajax({
                url: subscriberManagementAjax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_current_credit',
                    nonce: subscriberManagementAjax.nonce
                },
                timeout: 10000,
                success: function(response) {
                    if (response && response.success && response.data && response.data.credit !== undefined) {
                        console.log('💰 Current credit fetched successfully:', response.data.credit);
                        resolve(response.data.credit);
                    } else {
                        console.warn('⚠️ Invalid response from server:', response);
                        reject('Invalid response from server');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error fetching credit:', {xhr, status, error});

                    // Retry logic for network issues
                    if (retryCount < 2 && (status === 'timeout' || status === 'error')) {
                        console.log(`🔄 Retrying credit fetch (attempt ${retryCount + 1})...`);
                        setTimeout(() => {
                            fetchCurrentCreditValue(retryCount + 1).then(resolve).catch(reject);
                        }, 1000 * (retryCount + 1)); // Exponential backoff
                    } else {
                        reject(`AJAX error: ${error} (Status: ${status})`);
                    }
                }
            });
        });
    }

    // Export functions for external use
    window.subscriberManagementWidget = {
        switchSection: switchSection,
        showFeedbackMessage: showFeedbackMessage,
        formatCurrency: formatCurrency,
        updateCreditDisplay: updateCreditDisplay,
        syncCreditDisplays: syncCreditDisplays,
        fetchCurrentCreditValue: fetchCurrentCreditValue
    };

    // Also expose syncCreditDisplays globally for easy console access
    window.syncCreditDisplays = syncCreditDisplays;

    // ===========================================================================================
    // ADVANCED ERROR MONITORING SYSTEM - Integrated with Payment Gateway Checker
    // Accessible via console: window.AdvancedErrorMonitor
    // ===========================================================================================

    window.AdvancedErrorMonitor = {
        version: '2.0.0',
        isActive: false,
        errorCount: 0,
        errorHistory: [],
        performanceMetrics: {},
        autoFixEnabled: true,
        debugMode: false,

        // Initialize monitoring system
        init: function() {
            if (this.isActive) return;

            console.log('%c🔍 Advanced Error Monitor Initializing...', 'color: #E91E63; font-weight: bold;');

            this.setupGlobalErrorHandling();
            this.setupPerformanceMonitoring();
            this.setupNetworkMonitoring();
            this.setupDOMObserver();
            this.setupConsoleOverride();
            this.startMonitoring();

            this.isActive = true;
            console.log('%c✅ Advanced Error Monitor Active', 'color: #4CAF50; font-weight: bold;');

            // Add keyboard shortcut for debug panel
            this.setupKeyboardShortcuts();
        },

        // Global error handling
        setupGlobalErrorHandling: function() {
            const self = this;

            // JavaScript errors
            window.addEventListener('error', function(event) {
                self.logError({
                    type: 'JavaScript Error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error ? event.error.stack : 'No stack trace',
                    timestamp: new Date(),
                    severity: 'high'
                });
            });

            // Promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                self.logError({
                    type: 'Unhandled Promise Rejection',
                    message: event.reason?.message || event.reason,
                    stack: event.reason?.stack || 'No stack trace',
                    timestamp: new Date(),
                    severity: 'high'
                });
            });

            // AJAX errors
            const originalAjax = $.ajax;
            $.ajax = function(options) {
                const originalError = options.error;
                options.error = function(xhr, status, error) {
                    self.logError({
                        type: 'AJAX Error',
                        message: `${status}: ${error}`,
                        url: options.url,
                        method: options.type || 'GET',
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        timestamp: new Date(),
                        severity: 'medium'
                    });

                    if (originalError) {
                        originalError.apply(this, arguments);
                    }
                };
                return originalAjax.call(this, options);
            };
        },

        // Performance monitoring
        setupPerformanceMonitoring: function() {
            const self = this;

            // Monitor page load performance
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const timing = performance.timing;
                    self.performanceMetrics.pageLoad = {
                        loadTime: timing.loadEventEnd - timing.navigationStart,
                        domReady: timing.domContentLoadedEventEnd - timing.navigationStart,
                        firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
                    };

                    if (self.performanceMetrics.pageLoad.loadTime > 3000) {
                        self.logError({
                            type: 'Performance Warning',
                            message: `Slow page load: ${self.performanceMetrics.pageLoad.loadTime}ms`,
                            severity: 'low',
                            timestamp: new Date()
                        });
                    }
                }, 100);
            });

            // Monitor memory usage
            setInterval(function() {
                if ('memory' in performance) {
                    const memory = performance.memory;
                    const usedMB = (memory.usedJSHeapSize / 1048576).toFixed(2);

                    if (usedMB > 50) { // Alert if using more than 50MB
                        self.logError({
                            type: 'Memory Warning',
                            message: `High memory usage: ${usedMB}MB`,
                            severity: 'medium',
                            timestamp: new Date()
                        });
                    }
                }
            }, 30000); // Check every 30 seconds
        },

        // Network monitoring
        setupNetworkMonitoring: function() {
            const self = this;

            // Monitor network status
            window.addEventListener('online', function() {
                console.log('%c🌐 Network connection restored', 'color: #4CAF50;');
            });

            window.addEventListener('offline', function() {
                self.logError({
                    type: 'Network Error',
                    message: 'Network connection lost',
                    severity: 'high',
                    timestamp: new Date()
                });
            });
        },

        // DOM mutation observer
        setupDOMObserver: function() {
            const self = this;

            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // Check for removed elements that might cause issues
                    if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                        mutation.removedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const hasImportantClass = node.classList &&
                                    (node.classList.contains('payment-method') ||
                                     node.classList.contains('amount-btn') ||
                                     node.id === 'paypal-button-container');

                                if (hasImportantClass) {
                                    self.logError({
                                        type: 'DOM Warning',
                                        message: `Important element removed: ${node.tagName}${node.id ? '#' + node.id : ''}${node.className ? '.' + node.className.split(' ').join('.') : ''}`,
                                        severity: 'medium',
                                        timestamp: new Date()
                                    });
                                }
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },

        // Console override for better logging
        setupConsoleOverride: function() {
            const self = this;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.error = function(...args) {
                self.logError({
                    type: 'Console Error',
                    message: args.join(' '),
                    severity: 'medium',
                    timestamp: new Date()
                });
                originalError.apply(console, args);
            };

            console.warn = function(...args) {
                self.logError({
                    type: 'Console Warning',
                    message: args.join(' '),
                    severity: 'low',
                    timestamp: new Date()
                });
                originalWarn.apply(console, args);
            };
        },

        // Keyboard shortcuts
        setupKeyboardShortcuts: function() {
            const self = this;

            document.addEventListener('keydown', function(e) {
                // Ctrl+Shift+M for debug panel
                if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                    e.preventDefault();
                    self.showDebugPanel();
                }

                // Ctrl+Shift+R for error report
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    e.preventDefault();
                    self.generateErrorReport();
                }
            });
        },

        // Log error function
        logError: function(errorData) {
            this.errorCount++;
            this.errorHistory.push(errorData);

            // Keep only last 100 errors
            if (this.errorHistory.length > 100) {
                this.errorHistory.shift();
            }

            // Console output with styling
            const styles = {
                high: 'color: #f44336; font-weight: bold;',
                medium: 'color: #ff9800; font-weight: bold;',
                low: 'color: #2196f3;'
            };

            console.log(`%c🐛 ${errorData.type}: ${errorData.message}`, styles[errorData.severity] || styles.medium);

            // Auto-fix if enabled
            if (this.autoFixEnabled) {
                this.attemptAutoFix(errorData);
            }

            // Update debug panel if open
            this.updateDebugPanel();
        },

        // Auto-fix attempts
        attemptAutoFix: function(errorData) {
            const fixes = {
                'AJAX Error': () => {
                    if (errorData.message.includes('timeout')) {
                        console.log('%c🔧 Auto-fix: Extending AJAX timeout', 'color: #4CAF50;');
                        $.ajaxSetup({ timeout: 30000 });
                    }
                },
                'DOM Warning': () => {
                    if (errorData.message.includes('paypal-button-container')) {
                        console.log('%c🔧 Auto-fix: Recreating PayPal container', 'color: #4CAF50;');
                        if ($('#paypal-button-container').length === 0) {
                            $('.payment-methods').append('<div id="paypal-button-container"></div>');
                        }
                    }
                },
                'Network Error': () => {
                    console.log('%c🔧 Auto-fix: Setting up network retry', 'color: #4CAF50;');
                    // Could implement retry logic here
                }
            };

            const fix = fixes[errorData.type];
            if (fix) {
                try {
                    fix();
                } catch (e) {
                    console.error('Auto-fix failed:', e.message);
                }
            }
        },

        // Debug panel
        showDebugPanel: function() {
            // Remove existing panel
            $('#advanced-debug-panel').remove();

            const panel = $(`
                <div id="advanced-debug-panel" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 400px;
                    max-height: 80vh;
                    background: #1e1e1e;
                    color: #fff;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 99999;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    overflow: hidden;
                ">
                    <div style="background: #333; padding: 10px; border-bottom: 1px solid #555; display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: bold;">🔍 Advanced Debug Panel</span>
                        <button id="close-debug-panel" style="background: #f44336; color: white; border: none; border-radius: 3px; padding: 5px 10px; cursor: pointer;">✕</button>
                    </div>
                    <div style="padding: 10px; max-height: 60vh; overflow-y: auto;">
                        <div><strong>Status:</strong> ${this.isActive ? '🟢 Active' : '🔴 Inactive'}</div>
                        <div><strong>Errors:</strong> ${this.errorCount}</div>
                        <div><strong>Auto-fix:</strong> ${this.autoFixEnabled ? '✅ Enabled' : '❌ Disabled'}</div>
                        <hr style="border: 1px solid #555; margin: 10px 0;">

                        <div style="margin-bottom: 10px;">
                            <button id="run-gateway-check" style="background: #2196F3; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">Gateway Check</button>
                            <button id="simulate-error" style="background: #FF9800; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">Simulate Error</button>
                            <button id="export-errors" style="background: #4CAF50; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">Export Report</button>
                        </div>

                        <div id="debug-content">
                            <div><strong>Recent Errors:</strong></div>
                            <div id="error-list" style="max-height: 300px; overflow-y: auto; background: #2e2e2e; padding: 5px; border-radius: 3px; margin-top: 5px;">
                                ${this.getRecentErrorsHTML()}
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(panel);

            // Event handlers
            $('#close-debug-panel').click(() => panel.remove());
            $('#run-gateway-check').click(() => {
                if (window.PaymentGatewayChecker) {
                    window.PaymentGatewayChecker.runChecklist();
                }
            });
            $('#simulate-error').click(() => {
                if (window.PaymentGatewayChecker) {
                    window.PaymentGatewayChecker.simulateError('network');
                }
            });
            $('#export-errors').click(() => this.generateErrorReport());

            // Make draggable
            this.makeDraggable(panel[0]);
        },

        // Update debug panel content
        updateDebugPanel: function() {
            const errorList = $('#error-list');
            if (errorList.length > 0) {
                errorList.html(this.getRecentErrorsHTML());
            }
        },

        // Get recent errors HTML
        getRecentErrorsHTML: function() {
            if (this.errorHistory.length === 0) {
                return '<div style="color: #4CAF50;">No errors detected</div>';
            }

            return this.errorHistory.slice(-10).reverse().map(error => {
                const severityColors = {
                    high: '#f44336',
                    medium: '#ff9800',
                    low: '#2196f3'
                };

                return `
                    <div style="border-left: 3px solid ${severityColors[error.severity] || '#666'}; padding-left: 8px; margin-bottom: 8px;">
                        <div style="color: ${severityColors[error.severity] || '#666'}; font-weight: bold;">${error.type}</div>
                        <div style="font-size: 11px; color: #ccc;">${error.message}</div>
                        <div style="font-size: 10px; color: #999;">${error.timestamp.toLocaleTimeString()}</div>
                    </div>
                `;
            }).join('');
        },

        // Make element draggable
        makeDraggable: function(element) {
            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            const header = element.querySelector('div');

            header.onmousedown = dragMouseDown;

            function dragMouseDown(e) {
                e = e || window.event;
                e.preventDefault();
                pos3 = e.clientX;
                pos4 = e.clientY;
                document.onmouseup = closeDragElement;
                document.onmousemove = elementDrag;
            }

            function elementDrag(e) {
                e = e || window.event;
                e.preventDefault();
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;
                element.style.top = (element.offsetTop - pos2) + "px";
                element.style.left = (element.offsetLeft - pos1) + "px";
            }

            function closeDragElement() {
                document.onmouseup = null;
                document.onmousemove = null;
            }
        },

        // Generate comprehensive error report
        generateErrorReport: function() {
            const report = {
                timestamp: new Date().toISOString(),
                version: this.version,
                url: window.location.href,
                userAgent: navigator.userAgent,
                totalErrors: this.errorCount,
                errorHistory: this.errorHistory,
                performanceMetrics: this.performanceMetrics,
                browserInfo: {
                    language: navigator.language,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine
                },
                screenInfo: {
                    width: screen.width,
                    height: screen.height,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight
                }
            };

            console.log('%c📊 Error Report Generated', 'color: #4CAF50; font-weight: bold;');
            console.log(report);

            // Export as file
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `error-report-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            return report;
        },

        // Start monitoring
        startMonitoring: function() {
            console.log('%c👁️ Error monitoring started', 'color: #2196F3;');
            console.log('%cPress Ctrl+Shift+M to open debug panel', 'color: #666; font-style: italic;');
            console.log('%cPress Ctrl+Shift+R to generate error report', 'color: #666; font-style: italic;');
        },

        // Stop monitoring
        stop: function() {
            this.isActive = false;
            console.log('%c⏹️ Error monitoring stopped', 'color: #F44336; font-weight: bold;');
        },

        // Toggle auto-fix
        toggleAutoFix: function() {
            this.autoFixEnabled = !this.autoFixEnabled;
            console.log(`%c🔧 Auto-fix ${this.autoFixEnabled ? 'enabled' : 'disabled'}`,
                       `color: ${this.autoFixEnabled ? '#4CAF50' : '#F44336'}; font-weight: bold;`);
        },

        // Clear error history
        clearHistory: function() {
            this.errorHistory = [];
            this.errorCount = 0;
            console.log('%c🗑️ Error history cleared', 'color: #2196F3; font-weight: bold;');
        }
    };

    // Auto-initialize error monitor and credit synchronization
    $(document).ready(function() {
        window.AdvancedErrorMonitor.init();

        // Initialize credit synchronization after a short delay to ensure DOM is ready
        setTimeout(function() {
            if (typeof syncCreditDisplays === 'function') {
                syncCreditDisplays();
                console.log('%c💰 Credit synchronization initialized', 'color: #28a745; font-weight: bold;');

                // Set up periodic sync check every 30 seconds
                setInterval(function() {
                    syncCreditDisplays();
                }, 30000);

                console.log('%c⏰ Periodic credit sync enabled (every 30 seconds)', 'color: #17a2b8; font-weight: bold;');
            }
        }, 500);

        console.log('%c🛡️ Error Monitor Console Commands:', 'color: #E91E63; font-weight: bold;');
        console.log('  • AdvancedErrorMonitor.showDebugPanel() - Open debug interface');
        console.log('  • AdvancedErrorMonitor.generateErrorReport() - Export error report');
        console.log('  • AdvancedErrorMonitor.toggleAutoFix() - Toggle auto-fix feature');
        console.log('  • AdvancedErrorMonitor.clearHistory() - Clear error history');
        console.log('  • AdvancedErrorMonitor.stop() - Stop monitoring');
        console.log('  • syncCreditDisplays() - Manually sync credit displays');
        console.log('  • subscriberManagementWidget.fetchCurrentCreditValue() - Get current credit from server');
    });

    // ===========================================================================================
    // GRID LAYOUT ENFORCER FOR CONSUMPTION SECTION
    // Funzione per forzare il layout grid nella sezione consumi
    // ===========================================================================================

    window.GridLayoutEnforcer = {
        version: '1.0.0',
        isActive: false,
        checkInterval: null,

        // Initialize the grid layout enforcer
        init: function() {
            this.isActive = true;
            this.enforceGridLayout();
            this.startMonitoring();
            console.log('%c🔧 Grid Layout Enforcer initialized', 'color: #4CAF50; font-weight: bold;');
        },

        // Force grid layout on consumption section
        enforceGridLayout: function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (!consumptionSection) {
                console.warn('Consumption section not found');
                return;
            }

            // Find all stats grid elements in consumption section
            const statsGrids = consumptionSection.querySelectorAll('.stats-grid, .subscriber-stats-wrapper');

            statsGrids.forEach((grid, index) => {
                // Force grid layout with maximum specificity
                grid.style.setProperty('display', 'grid', 'important');
                grid.style.setProperty('grid-template-columns', 'repeat(auto-fit, minmax(200px, 1fr))', 'important');
                grid.style.setProperty('gap', '20px', 'important');

                // Reset any flex properties that might interfere
                grid.style.setProperty('flex-direction', 'initial', 'important');
                grid.style.setProperty('flex-wrap', 'initial', 'important');
                grid.style.setProperty('flex', 'initial', 'important');

                // Add enforcer classes for CSS targeting
                grid.classList.add('force-grid-layout', 'js-ensure-grid', 'subscriber-grid-enforced');

                // Add data attributes for maximum CSS specificity
                grid.setAttribute('data-widget-type', 'subscriber-management');
                grid.setAttribute('data-layout', 'grid');

                console.log(`Grid layout enforced on element ${index + 1}:`, grid);
            });

            // Also check for stats cards and ensure they don't interfere
            const statsCards = consumptionSection.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                // Reset flex properties that might be inherited
                card.style.setProperty('flex', 'initial', 'important');
                card.style.setProperty('width', 'auto', 'important');
                card.style.setProperty('min-width', 'initial', 'important');
                card.style.setProperty('max-width', 'initial', 'important');

                console.log(`Stats card ${index + 1} layout reset:`, card);
            });
        },

        // Start monitoring for layout changes
        startMonitoring: function() {
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
            }

            // Check every 2 seconds for layout disruptions
            this.checkInterval = setInterval(() => {
                if (this.isActive) {
                    this.checkAndFixLayout();
                }
            }, 2000);

            console.log('%c⏰ Grid layout monitoring started', 'color: #2196F3; font-weight: bold;');
        },

        // Check if layout is correct and fix if needed
        checkAndFixLayout: function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (!consumptionSection) return;

            const statsGrids = consumptionSection.querySelectorAll('.stats-grid, .subscriber-stats-wrapper');
            let needsFix = false;

            statsGrids.forEach(grid => {
                const computedStyle = window.getComputedStyle(grid);
                if (computedStyle.display !== 'grid') {
                    needsFix = true;
                    console.warn('Grid layout disrupted, fixing...', grid);
                }
            });

            if (needsFix) {
                this.enforceGridLayout();
                console.log('%c🔧 Grid layout automatically fixed', 'color: #FF9800; font-weight: bold;');
            }
        },

        // Stop monitoring
        stop: function() {
            this.isActive = false;
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
                this.checkInterval = null;
            }
            console.log('%c⏹️ Grid layout monitoring stopped', 'color: #F44336; font-weight: bold;');
        },

        // Manual fix function
        manualFix: function() {
            this.enforceGridLayout();
            console.log('%c🔧 Manual grid layout fix applied', 'color: #4CAF50; font-weight: bold;');
        },

        // Debug function to check current layout
        debugLayout: function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (!consumptionSection) {
                console.log('Consumption section not found');
                return;
            }

            console.log('%c🔍 Grid Layout Debug Info:', 'color: #9C27B0; font-weight: bold;');

            const statsGrids = consumptionSection.querySelectorAll('.stats-grid, .subscriber-stats-wrapper');
            statsGrids.forEach((grid, index) => {
                const computedStyle = window.getComputedStyle(grid);
                console.log(`Grid ${index + 1}:`, {
                    element: grid,
                    display: computedStyle.display,
                    gridTemplateColumns: computedStyle.gridTemplateColumns,
                    gap: computedStyle.gap,
                    flexDirection: computedStyle.flexDirection,
                    classes: Array.from(grid.classList),
                    attributes: Array.from(grid.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
                });
            });
        }
    };

    // Auto-initialize grid layout enforcer when consumption section is shown
    $(document).on('click', '.menu-item[data-section="consumption"]', function() {
        setTimeout(function() {
            if (window.GridLayoutEnforcer) {
                window.GridLayoutEnforcer.init();
            }
        }, 100);
    });

    // Initialize on document ready if consumption section is already active
    $(document).ready(function() {
        setTimeout(function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (consumptionSection && consumptionSection.classList.contains('active')) {
                if (window.GridLayoutEnforcer) {
                    window.GridLayoutEnforcer.init();
                }
            }
        }, 500);

        console.log('%c🛠️ Grid Layout Enforcer Console Commands:', 'color: #9C27B0; font-weight: bold;');
        console.log('  • GridLayoutEnforcer.init() - Initialize grid layout enforcer');
        console.log('  • GridLayoutEnforcer.manualFix() - Manually fix grid layout');
        console.log('  • GridLayoutEnforcer.debugLayout() - Debug current layout');
        console.log('  • GridLayoutEnforcer.stop() - Stop monitoring');
    });

    // ===========================================================================================
    // END ADVANCED ERROR MONITORING SYSTEM
    // ===========================================================================================

})(jQuery);
