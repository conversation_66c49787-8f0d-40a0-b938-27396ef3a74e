<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Isolamento Stili - Fix Applicato</title>
    <link rel="stylesheet" href="assets/css/document-stats.css">
    <link rel="stylesheet" href="assets/css/subscriber-management-widget.css">
    <link rel="stylesheet" href="assets/css/subscriber-stats-isolator.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        .widget-section {
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .widget-title {
            background: #3498db;
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .test-notes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Test Isolamento Stili - Fix Applicato</h1>
        <p><strong>Obiettivo:</strong> Verificare che il fix applicato al file <code>document-stats.css</code> impedisca la sovrascrittura degli stili del subscriber management widget.</p>
        
        <div class="comparison-grid">
            <!-- Document Viewer Widget -->
            <div class="widget-section">
                <div class="widget-title">
                    <span class="status-indicator status-ok"></span>
                    Document Viewer Widget (Flexbox)
                </div>
                
                <div class="stats-widget">
                    <div class="stats-grid">
                        <div class="stats-row usage-row">
                            <div class="stats-item">
                                <div class="stats-label">Analisi</div>
                                <div class="stats-value">42</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">Token</div>
                                <div class="stats-value">1,250</div>
                            </div>
                        </div>
                        <div class="stats-row costs-row">
                            <div class="stats-item">
                                <div class="stats-label">Costo Stimato</div>
                                <div class="stats-value cost-highlight">€0.15</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">Costo Effettivo</div>
                                <div class="stats-value cost-highlight">€0.12</div>
                            </div>
                        </div>
                        <div class="stats-row credit-row">
                            <div class="stats-item">
                                <div class="stats-label">Credito</div>
                                <div class="stats-value credit-highlight">€25.88</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscriber Management Widget -->
            <div class="widget-section">
                <div class="widget-title">
                    <span class="status-indicator status-ok"></span>
                    Subscriber Management Widget (Grid)
                </div>
                
                <div class="subscriber-management-widget-container">
                    <div class="subscriber-content-column">
                        <div id="consumption-section">
                            <div class="subscriber-stats-wrapper stats-grid" id="subscriber-stats-grid" data-widget-type="subscriber-management" data-layout="grid">
                                <div class="stats-card">
                                    <div class="stats-card-header">
                                        <i class="fas fa-chart-line stats-card-icon"></i>
                                        <span class="stats-card-title">Analisi</span>
                                    </div>
                                    <div class="stats-card-value">42</div>
                                    <div class="stats-card-description">Totale analisi effettuate</div>
                                </div>
                                
                                <div class="stats-card">
                                    <div class="stats-card-header">
                                        <i class="fas fa-coins stats-card-icon"></i>
                                        <span class="stats-card-title">Token</span>
                                    </div>
                                    <div class="stats-card-value">1,250</div>
                                    <div class="stats-card-description">Token utilizzati</div>
                                </div>
                                
                                <div class="stats-card">
                                    <div class="stats-card-header">
                                        <i class="fas fa-euro-sign stats-card-icon"></i>
                                        <span class="stats-card-title">Costo Stimato</span>
                                    </div>
                                    <div class="stats-card-value">€0.15</div>
                                    <div class="stats-card-description">Costo previsto</div>
                                </div>
                                
                                <div class="stats-card">
                                    <div class="stats-card-header">
                                        <i class="fas fa-receipt stats-card-icon"></i>
                                        <span class="stats-card-title">Costo Effettivo</span>
                                    </div>
                                    <div class="stats-card-value">€0.12</div>
                                    <div class="stats-card-description">Costo reale</div>
                                </div>
                                
                                <div class="stats-card credit-card">
                                    <div class="stats-card-header">
                                        <i class="fas fa-wallet stats-card-icon"></i>
                                        <span class="stats-card-title">Credito</span>
                                    </div>
                                    <div class="stats-card-value">€25.88</div>
                                    <div class="stats-card-description">Credito disponibile</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-notes">
            <h3>Note del Test:</h3>
            <ul>
                <li><strong>Document Viewer:</strong> Dovrebbe mantenere il layout flexbox verticale (colonna)</li>
                <li><strong>Subscriber Management:</strong> Dovrebbe mantenere il layout grid (griglia 2D)</li>
                <li><strong>Fix Applicato:</strong> Selettori CSS specifici con <code>:not(.subscriber-management-widget-container)</code></li>
                <li><strong>Isolamento:</strong> I due widget dovrebbero avere layout completamente indipendenti</li>
            </ul>
        </div>
    </div>

    <script>
        // Verifica che il layout sia corretto
        document.addEventListener('DOMContentLoaded', function() {
            const subscriberGrid = document.querySelector('.subscriber-stats-wrapper.stats-grid');
            const documentGrid = document.querySelector('.stats-widget .stats-grid');
            
            if (subscriberGrid) {
                const computedStyle = window.getComputedStyle(subscriberGrid);
                console.log('Subscriber Grid Display:', computedStyle.display);
                console.log('Subscriber Grid Template Columns:', computedStyle.gridTemplateColumns);
            }
            
            if (documentGrid) {
                const computedStyle = window.getComputedStyle(documentGrid);
                console.log('Document Grid Display:', computedStyle.display);
                console.log('Document Grid Flex Direction:', computedStyle.flexDirection);
            }
        });
    </script>
</body>
</html>
