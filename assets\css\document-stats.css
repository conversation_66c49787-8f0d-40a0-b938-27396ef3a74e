/* Stili per il documento stats
 *
 * Gestisce l'aspetto delle statistiche utente, inclusi i conteggi,
 * i grafici e la presentazione delle analisi recenti.
 */

/* Spinner inline per i bottoni durante le operazioni di disconnessione */
.spinner-inline {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 5px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Stile specifico per il pulsante di logout */
.stats-logout-link {
    position: relative;
    transition: all 0.3s ease;
    display: inline-block;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    color: #555;
    padding: 3px 8px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 11px;
}

.stats-logout-link:hover {
    background-color: #e9e9e9;
    text-decoration: none;
    color: #333;
}

/* Stato di logout in corso */
.stats-logout-link.logging-out {
    pointer-events: none;
    opacity: 0.7;
    background-color: #eaeaea;
}

/* Spesa stimata - stile base */
#cost-estimate,
#estimated-cost {
    display: inline-block !important; /* Forza la visualizzazione */
    min-width: 50px; /* Larghezza minima per evitare sfarfallii */
    text-align: right; /* Allineamento a destra per i numeri */
    font-family: monospace; /* Font a larghezza fissa per allineamento */
    font-weight: bold; /* Rende il testo più visibile */
    color: #333; /* Colore scuro per migliore leggibilità */
    transition: color 0.3s ease, transform 0.3s ease;
    opacity: 1 !important; /* Forza l'opacità */
    visibility: visible !important; /* Forza la visibilità */
}

/* Classe per nascondere elementi */
.hidden {
    display: none !important;
}

/* Layout principale delle statistiche - STILE MINIMALE */
.stats-column {
    background-color: #fafafa;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    padding: 10px;
    flex: 0 0 240px; /* Larghezza ridotta */
    max-width: 240px; /* Larghezza massima ridotta */
    box-sizing: border-box;
    margin-right: 15px; /* Margine ridotto */
    border: 1px solid #eaeaea;
    overflow: visible !important; /* Assicura che i tooltip non vengano tagliati */
}

/* Sezione delle statistiche con sfondo e bordi - STILE MINIMALE */
.stats-section {
    background-color: #ffffff;
    border-radius: 4px;
    border: none;
    margin-bottom: 10px; /* Ridotto da 15px */
    overflow: hidden;
}

.stats-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: #f5f5f5;
    border-bottom: 1px solid #eaeaea;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    margin-bottom: 0;
    font-size: 13px;
    font-weight: 500;
}

.stats-section-content {
    padding: 8px;
    background: #fff;
    border-radius: 0 0 4px 4px;
    transition: max-height 0.3s;
    position: relative;
    z-index: 1;
    overflow: visible !important;
}

/* Prevenzione allargamento container durante l'aggiornamento */
/* Specifico per document viewer per evitare conflitti con subscriber management widget */
/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .stats-grid:not(.subscriber-management-widget-container .stats-grid),
#user-stats-container .stats-grid:not(.subscriber-management-widget-container .stats-grid),
.document-viewer-widget .stats-grid:not(.subscriber-management-widget-container .stats-grid),
.document-viewer-stats-container .stats-grid:not(.subscriber-management-widget-container .stats-grid),
.document-viewer-container .stats-grid,
.document-stats-container .stats-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px;
    margin-bottom: 10px;
    width: 100%;
    max-width: 100%;
    overflow: visible !important;
}

/* Nuovo layout a righe per le statistiche - COMPATTO */
/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .stats-row:not(.subscriber-management-widget-container .stats-row),
#user-stats-container .stats-row:not(.subscriber-management-widget-container .stats-row),
.document-viewer-widget .stats-row:not(.subscriber-management-widget-container .stats-row),
.document-viewer-stats-container .stats-row:not(.subscriber-management-widget-container .stats-row),
.document-viewer-container .stats-row,
.document-stats-container .stats-row {
    display: flex !important;
    gap: 8px;
    width: 100%;
    overflow: visible !important;
    margin-bottom: 0;
}

/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .usage-row:not(.subscriber-management-widget-container .usage-row),
.stats-widget .costs-row:not(.subscriber-management-widget-container .costs-row),
.stats-widget .credit-row:not(.subscriber-management-widget-container .credit-row),
.stats-widget .total-cost-row:not(.subscriber-management-widget-container .total-cost-row),
#user-stats-container .usage-row:not(.subscriber-management-widget-container .usage-row),
#user-stats-container .costs-row:not(.subscriber-management-widget-container .costs-row),
#user-stats-container .credit-row:not(.subscriber-management-widget-container .credit-row),
#user-stats-container .total-cost-row:not(.subscriber-management-widget-container .total-cost-row),
.document-viewer-widget .usage-row:not(.subscriber-management-widget-container .usage-row),
.document-viewer-widget .costs-row:not(.subscriber-management-widget-container .costs-row),
.document-viewer-widget .credit-row:not(.subscriber-management-widget-container .credit-row),
.document-viewer-widget .total-cost-row:not(.subscriber-management-widget-container .total-cost-row),
.document-viewer-stats-container .usage-row:not(.subscriber-management-widget-container .usage-row),
.document-viewer-stats-container .costs-row:not(.subscriber-management-widget-container .costs-row),
.document-viewer-stats-container .credit-row:not(.subscriber-management-widget-container .credit-row),
.document-viewer-stats-container .total-cost-row:not(.subscriber-management-widget-container .total-cost-row),
.document-viewer-container .usage-row,
.document-viewer-container .costs-row,
.document-viewer-container .credit-row,
.document-viewer-container .total-cost-row,
.document-stats-container .usage-row,
.document-stats-container .costs-row,
.document-stats-container .credit-row,
.document-stats-container .total-cost-row {
    width: 100%;
}

/* Colori personalizzati per ciascuna tipologia di costo - MINIMIZZATI */
/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .costs-row .stats-item:not(.subscriber-management-widget-container .stats-item),
#user-stats-container .costs-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-widget .costs-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-stats-container .costs-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-container .costs-row .stats-item,
.document-stats-container .costs-row .stats-item {
    background-color: #fff;
    border: 1px solid #eee;
}

.stats-widget .total-cost-row .stats-item:not(.subscriber-management-widget-container .stats-item),
#user-stats-container .total-cost-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-widget .total-cost-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-stats-container .total-cost-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-container .total-cost-row .stats-item,
.document-stats-container .total-cost-row .stats-item {
    background-color: #fff;
    border: 1px solid #eee;
}

.stats-widget .credit-row .stats-item:not(.subscriber-management-widget-container .stats-item),
#user-stats-container .credit-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-widget .credit-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-stats-container .credit-row .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-container .credit-row .stats-item,
.document-stats-container .credit-row .stats-item {
    background-color: #fff;
    border: 1px solid #eee;
    padding: 8px;
}

/* Stili per elementi statistici - MINIMALE */
/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .stats-item:not(.subscriber-management-widget-container .stats-item),
#user-stats-container .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-widget .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-stats-container .stats-item:not(.subscriber-management-widget-container .stats-item),
.document-viewer-container .stats-item,
.document-stats-container .stats-item {
    overflow: visible;
    position: relative;
    flex: 1;
    background: #fff;
    border-radius: 4px;
    padding: 8px;
    position: relative;
    border: 1px solid #eee;
    transition: all 0.2s ease;
    text-align: center;
    overflow: visible !important;
}

.stats-widget .stats-item:hover:not(.subscriber-management-widget-container .stats-item),
#user-stats-container .stats-item:hover:not(.subscriber-management-widget-container .stats-item),
.document-viewer-widget .stats-item:hover:not(.subscriber-management-widget-container .stats-item),
.document-viewer-stats-container .stats-item:hover:not(.subscriber-management-widget-container .stats-item),
.document-viewer-container .stats-item:hover,
.document-stats-container .stats-item:hover {
    background: #f9f9f9;
}

/* Stile migliorato per l'etichetta statistica - MINIMALE */
/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .stats-label:not(.subscriber-management-widget-container .stats-label),
#user-stats-container .stats-label:not(.subscriber-management-widget-container .stats-label),
.document-viewer-widget .stats-label:not(.subscriber-management-widget-container .stats-label),
.document-viewer-stats-container .stats-label:not(.subscriber-management-widget-container .stats-label),
.document-viewer-container .stats-label,
.document-stats-container .stats-label {
    font-size: 12px;
    color: #555;
    margin-bottom: 4px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    overflow: visible !important;
}

.stats-widget .stats-label .stats-tooltip:not(.subscriber-management-widget-container .stats-tooltip),
#user-stats-container .stats-label .stats-tooltip:not(.subscriber-management-widget-container .stats-tooltip),
.document-viewer-widget .stats-label .stats-tooltip:not(.subscriber-management-widget-container .stats-tooltip),
.document-viewer-stats-container .stats-label .stats-tooltip:not(.subscriber-management-widget-container .stats-tooltip),
.document-viewer-container .stats-label .stats-tooltip,
.document-stats-container .stats-label .stats-tooltip {
    left: 50%;
}

/* CORREZIONE: miglioramento dell'icona info e del tooltip - MINIMALE */
.stats-info-icon {
    position: relative;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #f0f0f0;
    color: #999;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    font-style: italic;
    margin-left: 4px;
    cursor: help;
    border: none;
    transition: all 0.2s ease;
}

.stats-info-icon:hover {
    background: #e0e0e0;
}

/* CORREZIONE: tooltip con posizionamento absolute ma con z-index elevato */
.stats-tooltip {
    position: absolute;
    background: rgba(51, 51, 51, 0.95);
    color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: normal;
    width: 180px;
    z-index: 9999;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transform: translateX(-50%);
    bottom: 130%;
    left: 50%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
    margin: 0 auto;
    text-align: center;
    max-width: calc(100vw - 40px);
    word-break: normal;
    word-wrap: break-word;
}

/* CORREZIONE: freccia del tooltip - posizionata SOTTO il tooltip puntando verso l'icona */
.stats-tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: rgba(51, 51, 51, 0.95);
}

/* IMPORTANTE: Questi selettori sono specifici per il document viewer e NON devono influenzare il subscriber management widget */
.stats-widget .stats-value:not(.subscriber-management-widget-container .stats-value),
#user-stats-container .stats-value:not(.subscriber-management-widget-container .stats-value),
.document-viewer-widget .stats-value:not(.subscriber-management-widget-container .stats-value),
.document-viewer-stats-container .stats-value:not(.subscriber-management-widget-container .stats-value),
.document-viewer-container .stats-value,
.document-stats-container .stats-value {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

/* Colori personalizzati per valori - MINIMALI */
.cost-highlight {
    color: #e67e22;
}

.total-cost-highlight {
    color: #d35400;
    font-weight: 500;
    font-size: 16px;
}

.credit-highlight {
    color: #27ae60;
    font-size: 16px;
}

/* Effetto di aggiornamento per i valori */
.highlight-update {
    animation: highlight-update 2s ease-out 1;
    font-weight: bold;
}

@keyframes highlight-update {
    0% { background-color: rgba(255, 222, 173, 0.9); color: #d35400; }
    50% { background-color: rgba(255, 222, 173, 0.6); }
    100% { background-color: transparent; color: inherit; }
}

/* Date info - RIMOSSA */

/* Pulsante aggiornamento - MINIMALE */
.refresh-stats-btn {
    background: #f5f5f5;
    border: none;
    padding: 5px 8px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 6px;
    font-size: 12px;
    display: block;
    width: 100%;
    color: #555;
    text-align: center;
    transition: all 0.2s ease;
}

.refresh-stats-btn:hover {
    background: #e0e0e0;
}

/* Icona toggle - MINIMALE */
.toggle-icon {
    width: 12px;
    height: 12px;
    position: relative;
    transition: transform 0.3s;
}

.toggle-icon:before, .toggle-icon:after {
    content: '';
    position: absolute;
    background-color: #777;
    transition: transform 0.3s;
}

.toggle-icon:before {
    width: 2px;
    height: 12px;
    top: 0;
    left: 5px;
}

.toggle-icon:after {
    width: 12px;
    height: 2px;
    top: 5px;
    left: 0;
}

.toggle-icon.expanded:after {
    opacity: 0;
    transform: rotate(90deg);
}

.toggle-icon.collapsed:before {
    transform: rotate(90deg);
}

/* Analisi recenti - MINIMALE */
.recent-analyses-list {
    list-style: none;
    padding: 0;
    margin: 0;
    margin-top: 6px;
}

.recent-analysis-item {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.recent-analysis-item:last-child {
    border-bottom: none;
}

.recent-analysis-item:hover {
    background-color: #f5f5f5;
}

.analysis-item-title {
    font-weight: 500;
    margin-bottom: 2px;
    color: #333;
}

.analysis-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #777;
}

.no-analyses {
    font-style: italic;
    color: #999;
    text-align: center;
    padding: 10px 0;
    font-size: 12px;
}

/* Stati di loading e errore - MINIMALE */
.stats-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #777;
    flex-direction: column;
    font-size: 12px;
}

.stats-spinner {
    border: 2px solid #f5f5f5;
    border-radius: 50%;
    border-top: 2px solid #999;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stats-error {
    color: #e74c3c;
    padding: 10px;
    text-align: center;
    background-color: #fef5f5;
    border-radius: 3px;
    font-size: 12px;
}

#retry-stats-btn {
    margin-top: 6px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

#retry-stats-btn:hover {
    background-color: #e6e6e6;
}

/* Responsività */
@media (max-width: 768px) {
    .stats-row {
        flex-direction: column;
    }

    .stats-tooltip {
        width: 160px;
        left: 50%;
        transform: translateX(-50%);
        top: auto;
        bottom: 130%;
        max-width: calc(100vw - 20px);
    }
}

/* Stili per il costo totale - MINIMALE */
.total-cost-row {
    padding: 4px 0;
    border-bottom: none;
    background-color: transparent;
}

/* Usa valori opzionali per il tema */
:root {
    --stats-border-color: #eaeaea;
    --stats-highlight-bg: #fafafa;
    --stats-cost-color: #d35400;
    --stats-bg-color: #ffffff;
    --stats-text-color: #333333;
    --stats-accent-color: #27ae60;
}

/* Stile minimal per la user-info */
.user-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 8px;
    background: #f0f0f0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
}

.user-role {
    font-size: 11px;
    color: #777;
}

/* Stili per il pulsante logout nel pannello statistiche del widget */
.user-info .stats-logout-button {
    margin-left: auto;
}

/* Highlight animation per elementi aggiornati */
@keyframes flash-animation {
    0% { background-color: rgba(255, 240, 165, 0); }
    20% { background-color: rgba(255, 240, 165, 0.5); }
    80% { background-color: rgba(255, 240, 165, 0.3); }
    100% { background-color: rgba(255, 240, 165, 0); }
}

/* Stili per il pulsante di logout nel pannello statistiche */
.stats-widget-header {
    padding: 10px;
    background: #f9f9f9;
    border-bottom: 1px solid #eaeaea;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stats-user-info {
    display: flex;
    align-items: center;
}

.stats-avatar {
    margin-right: 10px;
}

.stats-user-details {
    flex: 1;
}

.stats-user-name {
    font-weight: bold;
    font-size: 14px;
    color: #333;
}

.stats-user-role {
    font-size: 12px;
    color: #666;
}

.stats-logout-button {
    margin-left: auto;
}

.stats-logout-link {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    background: #f1f1f1;
    border-radius: 3px;
    color: #333;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.stats-logout-link:hover {
    background: #e4e4e4;
    color: #d63638;  /* Colore rosso per indicare logout */
}

.stats-logout-link .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 3px;
}

/* Stili per la testata del pannello statistiche con utente e tipo sottoscrizione */
.user-details {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f9f9f9;
    border-bottom: 1px solid #eaeaea;
    margin-bottom: 10px;
}

#user-avatar {
    margin-right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e5e5e5;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #555;
    background-color: #e5e5e5;
}

#user-info {
    flex: 1;
}

#user-name {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

#user-subscription-type {
    font-size: 12px;
    color: #666;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}