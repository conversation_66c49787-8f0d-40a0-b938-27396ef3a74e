<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Ordine Caricamento CSS - Document Stats vs Subscriber Management</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .loading-order-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .loading-order-info h3 {
            color: #0c5460;
            margin-top: 0;
        }
        .css-file {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }
        .css-file.loaded-first {
            border-left: 4px solid #28a745;
        }
        .css-file.loaded-last {
            border-left: 4px solid #dc3545;
        }
        .css-file.isolated {
            border-left: 4px solid #ffc107;
        }
        .file-name {
            font-weight: bold;
            color: #495057;
        }
        .file-description {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .loading-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-first {
            background: #d4edda;
            color: #155724;
        }
        .status-last {
            background: #f8d7da;
            color: #721c24;
        }
        .status-isolated {
            background: #fff3cd;
            color: #856404;
        }
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        .widget-demo {
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .widget-title {
            background: #3498db;
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
        }
        .test-notes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Test Ordine Caricamento CSS - Fix Applicato</h1>
        
        <div class="loading-order-info">
            <h3>🔧 Modifica Implementata</h3>
            <p><strong>Problema risolto:</strong> Il CSS <code>document-stats.css</code> veniva caricato globalmente dal file principale, causando conflitti con il subscriber management widget.</p>
            <p><strong>Soluzione:</strong> Spostato il caricamento del CSS nel metodo <code>render_document_viewer()</code> per caricarlo solo quando necessario.</p>
        </div>

        <h2>Ordine di Caricamento CSS - PRIMA del Fix</h2>
        <div class="css-file loaded-first">
            <div class="loading-status status-first">GLOBALE</div>
            <div class="file-name">document-stats.css</div>
            <div class="file-description">Caricato globalmente dal file principale - CAUSAVA CONFLITTI</div>
        </div>
        
        <div class="css-file loaded-first">
            <div class="loading-status status-first">GLOBALE</div>
            <div class="file-name">subscriber-management-widget.css</div>
            <div class="file-description">Caricato dal widget subscriber management</div>
        </div>
        
        <div class="css-file loaded-last">
            <div class="loading-status status-last">ULTIMO</div>
            <div class="file-name">subscriber-stats-isolator.css</div>
            <div class="file-description">Tentativo di isolamento - NON SEMPRE EFFICACE</div>
        </div>

        <h2>Ordine di Caricamento CSS - DOPO il Fix</h2>
        <div class="css-file loaded-first">
            <div class="loading-status status-first">GLOBALE</div>
            <div class="file-name">subscriber-management-widget.css</div>
            <div class="file-description">Caricato dal widget subscriber management</div>
        </div>
        
        <div class="css-file loaded-last">
            <div class="loading-status status-last">ULTIMO</div>
            <div class="file-name">subscriber-stats-isolator.css</div>
            <div class="file-description">Isolamento completo con massima specificità</div>
        </div>
        
        <div class="css-file isolated">
            <div class="loading-status status-isolated">ON-DEMAND</div>
            <div class="file-name">document-stats.css</div>
            <div class="file-description">Caricato SOLO quando il document viewer è utilizzato</div>
        </div>

        <h2>Codice Implementato</h2>
        <div class="code-block">
public function render_document_viewer($atts) {
    // Carica il CSS document-stats solo quando il widget document viewer viene utilizzato
    wp_enqueue_style('document-stats-style', 
        $this->plugin_url . 'assets/css/document-stats.css',
        array('document-viewer-style'),
        $this->version
    );
    
    // ... resto del metodo
}
        </div>

        <h2>Vantaggi della Soluzione</h2>
        <div class="comparison-section">
            <div class="widget-demo">
                <div class="widget-title">✅ Vantaggi</div>
                <ul>
                    <li><strong>Caricamento Condizionale:</strong> CSS caricato solo quando necessario</li>
                    <li><strong>Nessun Conflitto:</strong> I widget non si influenzano reciprocamente</li>
                    <li><strong>Performance:</strong> Meno CSS caricato globalmente</li>
                    <li><strong>Manutenibilità:</strong> Ogni widget gestisce i propri stili</li>
                    <li><strong>Isolamento:</strong> Modifiche a un widget non influenzano altri</li>
                </ul>
            </div>
            
            <div class="widget-demo">
                <div class="widget-title">🎯 Risultati</div>
                <ul>
                    <li><strong>Subscriber Management:</strong> Layout grid mantenuto</li>
                    <li><strong>Document Viewer:</strong> Layout flexbox quando utilizzato</li>
                    <li><strong>Ordine Corretto:</strong> CSS caricato nell'ordine giusto</li>
                    <li><strong>Specificità:</strong> Regole CSS con priorità corretta</li>
                    <li><strong>Stabilità:</strong> Nessuna interferenza tra widget</li>
                </ul>
            </div>
        </div>

        <div class="test-notes">
            <h3>📋 Note Tecniche:</h3>
            <ul>
                <li><strong>File Modificato:</strong> <code>document-advisor-plugin.php</code> - metodo <code>enqueue_scripts()</code> e <code>render_document_viewer()</code></li>
                <li><strong>CSS Rimosso:</strong> Caricamento globale di <code>document-stats.css</code></li>
                <li><strong>CSS Aggiunto:</strong> Caricamento condizionale nel metodo render</li>
                <li><strong>Dipendenze:</strong> <code>document-stats.css</code> dipende da <code>document-viewer-style</code></li>
                <li><strong>Compatibilità:</strong> Mantiene la compatibilità con tutto il codice esistente</li>
            </ul>
        </div>

        <h2>Test di Verifica</h2>
        <div class="widget-demo">
            <div class="widget-title">🧪 Come Testare</div>
            <ol>
                <li><strong>Pagina senza Document Viewer:</strong> <code>document-stats.css</code> non dovrebbe essere caricato</li>
                <li><strong>Pagina con Document Viewer:</strong> <code>document-stats.css</code> dovrebbe essere caricato dopo <code>document-viewer-style</code></li>
                <li><strong>Subscriber Management Widget:</strong> Dovrebbe mantenere il layout grid senza interferenze</li>
                <li><strong>Strumenti Sviluppatore:</strong> Verificare l'ordine di caricamento CSS nella tab Network</li>
                <li><strong>Computed Styles:</strong> Verificare che ogni widget abbia gli stili corretti</li>
            </ol>
        </div>
    </div>

    <script>
        // Verifica che il CSS sia caricato correttamente
        document.addEventListener('DOMContentLoaded', function() {
            console.log('%c🔍 CSS Loading Order Test', 'color: #2c3e50; font-weight: bold; font-size: 16px;');
            
            // Controlla se document-stats.css è caricato
            const stylesheets = Array.from(document.styleSheets);
            const documentStatsLoaded = stylesheets.some(sheet => 
                sheet.href && sheet.href.includes('document-stats.css')
            );
            
            console.log('Document Stats CSS loaded:', documentStatsLoaded);
            
            // Lista tutti i CSS caricati
            console.log('All loaded stylesheets:');
            stylesheets.forEach((sheet, index) => {
                if (sheet.href) {
                    const filename = sheet.href.split('/').pop();
                    console.log(`${index + 1}. ${filename}`);
                }
            });
        });
    </script>
</body>
</html>
